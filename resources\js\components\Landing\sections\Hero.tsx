import React, { useState, useEffect } from 'react';
import { Search, UserPlus, BookOpen, MapPin, Star, Users, Shield, Clock, ChevronDown, Play, CheckCircle } from 'lucide-react';

// Theme props interface
interface ThemeProps {
  currentTheme: 'light' | 'dark';
  toggleTheme: () => void;
  getThemeIcon: () => React.ReactElement;
}

// Trust indicators data
const trustIndicators = [
  { icon: <Shield className="w-5 h-5" />, text: "Verifikasi <PERSON>amanan", value: "100%" },
  { icon: <Users className="w-5 h-5" />, text: "Pengguna Aktif", value: "5000+" },
  { icon: <Star className="w-5 h-5" />, text: "Rating Rata-rata", value: "4.8/5" },
  { icon: <Clock className="w-5 h-5" />, text: "Respon Cepat", value: "< 1 Jam" }
];

// Popular locations for quick access
const popularLocations = [
  "Jakarta", "<PERSON>ung", "Yogyakarta", "Surabaya", "Semarang"
];

const Hero: React.FC<ThemeProps> = ({ currentTheme, toggleTheme, getThemeIcon }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentLocationIndex, setCurrentLocationIndex] = useState(0);

  // Animate elements on mount
  useEffect(() => {
    setIsVisible(true);

    // Rotate through popular locations
    const interval = setInterval(() => {
      setCurrentLocationIndex((prev) => (prev + 1) % popularLocations.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Smooth scroll to search section
  const scrollToSearch = () => {
    const searchSection = document.getElementById('quick-search');
    if (searchSection) {
      searchSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-slate-50 dark:bg-slate-900 pt-16 sm:pt-20 md:pt-24 lg:pt-28 transition-colors duration-300">
      {/* Optimized Background with Performance */}
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full bg-gradient-to-br from-slate-100 via-slate-200 to-slate-300 dark:from-slate-800 dark:via-slate-900 dark:to-slate-950"></div>
        {/* Subtle pattern overlay for texture */}
        <div className="absolute inset-0 opacity-30 dark:opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      {/* Main Content Container - Mobile-First Design */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 py-6 sm:py-8 md:py-12 lg:py-16 xl:py-20 flex items-center justify-center min-h-[calc(100vh-4rem)] sm:min-h-[calc(100vh-5rem)] md:min-h-[calc(100vh-6rem)]">

        {/* Hero Content Grid - Mobile-First Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center w-full">

          {/* Left Column - Main Content (Mobile-First) */}
          <div className={`lg:col-span-7 flex flex-col justify-center text-center lg:text-left transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}>

            {/* Trust Badge - Mobile-Optimized */}
            <div className="inline-flex items-center gap-2 px-3 py-2 sm:px-4 sm:py-2.5 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-full border border-slate-200 dark:border-slate-700 mb-4 sm:mb-6 shadow-sm mx-auto lg:mx-0">
              <div className="flex items-center gap-1">
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-500 fill-current" />
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-500 fill-current" />
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-500 fill-current" />
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-500 fill-current" />
                <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-500 fill-current" />
              </div>
              <span className="text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300">
                Dipercaya 5000+ pengguna
              </span>
            </div>

            {/* Dynamic Headline - Mobile-Responsive */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-slate-900 dark:text-white mb-4 sm:mb-6 leading-tight tracking-tight">
              Temukan Kost Impian di{' '}
              <span className="relative inline-block">
                <span className="text-slate-600 dark:text-slate-400 transition-all duration-500">
                  {popularLocations[currentLocationIndex]}
                </span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-slate-600 dark:bg-slate-400 rounded-full transform scale-x-0 animate-pulse"></div>
              </span>
            </h1>

            {/* Value Proposition - Mobile-Responsive */}
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-6 sm:mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
              Platform terpercaya untuk mencari kost yang{' '}
              <span className="font-semibold text-slate-800 dark:text-slate-100">aman, nyaman, dan terjangkau</span>{' '}
              di seluruh Indonesia.
            </p>

            {/* Key Benefits - Mobile-Responsive Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8 max-w-2xl mx-auto lg:mx-0">
              {[
                { icon: <Shield className="w-4 h-4 sm:w-5 sm:h-5" />, text: "Verifikasi Keamanan" },
                { icon: <Clock className="w-4 h-4 sm:w-5 sm:h-5" />, text: "Respon Cepat < 1 Jam" },
                { icon: <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5" />, text: "Proses Mudah & Cepat" },
                { icon: <Users className="w-4 h-4 sm:w-5 sm:h-5" />, text: "Komunitas Terpercaya" }
              ].map((benefit, index) => (
                <div
                  key={benefit.text}
                  className={`flex items-center gap-2 sm:gap-3 p-3 sm:p-3 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-lg border border-slate-200/50 dark:border-slate-700/50 transition-all duration-700 ${
                    isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                  }`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <div className="text-slate-600 dark:text-slate-400 flex-shrink-0">
                    {benefit.icon}
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300">
                    {benefit.text}
                  </span>
                </div>
              ))}
            </div>

            {/* Primary Call-to-Action Buttons - Mobile-Responsive */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start max-w-2xl mx-auto lg:mx-0">
              {/* Primary CTA - Mobile-Optimized */}
              <button
                onClick={scrollToSearch}
                className="group relative inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-slate-800 dark:bg-slate-200 text-white dark:text-slate-800 font-bold rounded-xl hover:bg-slate-700 dark:hover:bg-slate-100 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 min-h-[48px] sm:min-h-[56px] overflow-hidden"
                aria-label="Mulai mencari kost sekarang"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-slate-700 to-slate-800 dark:from-slate-100 dark:to-slate-200 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Search className="relative mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 transition-transform duration-300 group-hover:scale-110" />
                <span className="relative text-sm sm:text-lg font-semibold">Cari Kost Sekarang</span>
              </button>

              {/* Secondary CTA - Mobile-Optimized */}
              <button
                onClick={() => {
                  // Scroll to registration or open modal
                  console.log('Register clicked');
                }}
                className="group inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 border-2 border-slate-800 dark:border-slate-200 text-slate-800 dark:text-slate-200 font-semibold rounded-xl hover:bg-slate-800 dark:hover:bg-slate-200 hover:text-white dark:hover:text-slate-800 transition-all duration-300 min-h-[48px] sm:min-h-[56px]"
                aria-label="Daftar akun gratis"
              >
                <UserPlus className="mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 transition-transform duration-300 group-hover:scale-110" />
                <span className="text-sm sm:text-lg">Daftar Gratis</span>
              </button>
            </div>

            {/* Quick Search Hint - Mobile-Responsive */}
            <div className="mt-4 sm:mt-6 text-center lg:text-left">
              <p className="text-xs sm:text-sm text-slate-500 dark:text-slate-400 px-2 sm:px-0">
                💡 <span className="font-medium">Tips:</span> Coba cari "Jakarta Selatan" atau "Bandung" untuk hasil terbaik
              </p>
            </div>
          </div>

          {/* Right Column - Visual Content & Social Proof (Desktop Only) */}
          <div className={`lg:col-span-5 hidden lg:flex lg:flex-col lg:justify-center transition-all duration-1000 delay-300 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}>

            {/* Statistics Cards - Social Proof with Better Responsive Design */}
            <div className="grid grid-cols-2 gap-3 md:gap-4 lg:gap-4 mb-6 md:mb-8">
              {[
                { number: "1000+", label: "Kost Tersedia", icon: <MapPin className="w-4 h-4 lg:w-5 lg:h-5" /> },
                { number: "50+", label: "Kota di Indonesia", icon: <Users className="w-4 h-4 lg:w-5 lg:h-5" /> },
                { number: "5000+", label: "Pengguna Aktif", icon: <Star className="w-4 h-4 lg:w-5 lg:h-5" /> },
                { number: "4.8/5", label: "Rating Rata-rata", icon: <Shield className="w-4 h-4 lg:w-5 lg:h-5" /> }
              ].map((stat, index) => (
                <div
                  key={stat.label}
                  className={`bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm p-3 md:p-4 lg:p-6 rounded-lg md:rounded-xl border border-slate-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-105 ${
                    isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                  }`}
                  style={{ transitionDelay: `${(index + 4) * 100}ms` }}
                >
                  <div className="flex items-center gap-2 md:gap-3 mb-1 md:mb-2">
                    <div className="text-slate-600 dark:text-slate-400 flex-shrink-0">
                      {stat.icon}
                    </div>
                    <div className="text-lg md:text-xl lg:text-2xl font-bold text-slate-800 dark:text-white">
                      {stat.number}
                    </div>
                  </div>
                  <div className="text-xs md:text-sm font-medium text-slate-600 dark:text-slate-400">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>

            {/* Trust Indicators - Enhanced Responsive Design */}
            <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm p-4 md:p-5 lg:p-6 rounded-lg md:rounded-xl border border-slate-200/50 dark:border-slate-700/50">
              <h3 className="text-base md:text-lg font-semibold text-slate-800 dark:text-white mb-3 md:mb-4">
                Mengapa Memilih Satu Atap?
              </h3>
              <div className="space-y-2 md:space-y-3">
                {trustIndicators.map((indicator, index) => (
                  <div
                    key={indicator.text}
                    className={`flex items-center justify-between p-2.5 md:p-3 bg-slate-50/50 dark:bg-slate-700/50 rounded-lg transition-all duration-500 ${
                      isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
                    }`}
                    style={{ transitionDelay: `${(index + 8) * 100}ms` }}
                  >
                    <div className="flex items-center gap-2 md:gap-3">
                      <div className="text-slate-600 dark:text-slate-400 flex-shrink-0">
                        {indicator.icon}
                      </div>
                      <span className="text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300">
                        {indicator.text}
                      </span>
                    </div>
                    <span className="text-xs md:text-sm font-bold text-slate-800 dark:text-white flex-shrink-0">
                      {indicator.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Statistics Section - Mobile & Tablet */}
        <div className="lg:hidden mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-slate-300/50 dark:border-slate-600/50">
          <div className="text-center mb-4 sm:mb-6">
            <h3 className="text-lg sm:text-xl font-bold text-slate-800 dark:text-white mb-1 sm:mb-2">
              Dipercaya Ribuan Pengguna
            </h3>
            <p className="text-sm sm:text-sm text-slate-600 dark:text-slate-400">
              Platform kost terpercaya di Indonesia
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
            {[
              { number: "1000+", label: "Kost Tersedia" },
              { number: "50+", label: "Kota" },
              { number: "5000+", label: "Pengguna" },
              { number: "4.8/5", label: "Rating" }
            ].map((stat, index) => (
              <div
                key={stat.label}
                className={`text-center p-3 sm:p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg border border-slate-200/50 dark:border-slate-700/50 transition-all duration-500 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                }`}
                style={{ transitionDelay: `${(index + 12) * 100}ms` }}
              >
                <div className="text-xl sm:text-2xl md:text-3xl font-bold text-slate-800 dark:text-white mb-1">
                  {stat.number}
                </div>
                <div className="text-xs sm:text-sm font-medium text-slate-600 dark:text-slate-400">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Scroll Indicator - Mobile-Responsive */}
      <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <button
          onClick={scrollToSearch}
          className="group flex flex-col items-center space-y-1 sm:space-y-2 p-2 sm:p-3 hover:bg-white/10 dark:hover:bg-slate-800/10 rounded-lg transition-all duration-300"
          aria-label="Scroll ke bagian pencarian"
        >
          <span className="text-slate-600 dark:text-slate-300 text-xs sm:text-sm font-medium opacity-80 group-hover:opacity-100 transition-opacity duration-300">
            Mulai Pencarian
          </span>
          <div className="animate-bounce group-hover:animate-pulse">
            <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-colors duration-300" />
          </div>
        </button>
      </div>

      {/* Accessibility: Skip to main content */}
      <a
        href="#quick-search"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-slate-800 focus:text-white focus:rounded-lg focus:shadow-lg"
      >
        Lewati ke pencarian utama
      </a>
    </section>
  );
};

export default Hero;
